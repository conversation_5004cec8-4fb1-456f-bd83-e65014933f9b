import asyncio
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.prebuilt import create_react_agent
from vups.mcp import get_mcp_server_dict

class MCPClient:
    """A client for interacting with multiple MCP servers."""
    def __init__(self, llm: str, servers: dict = None):
        if servers is None:
            servers = get_mcp_server_dict()
        self.client = MultiServerMCPClient(servers)
        self.llm = llm
        self.tools = None
        self.agent = None

    async def initialize(self):
        """Initialize the client by loading tools and creating the agent."""
        self.tools = await self.client.get_tools()
        self.agent = create_react_agent(self.llm, self.tools)

    async def ainvoke(self, messages) -> dict:
        if self.agent is None:
            await self.initialize()
        return await self.agent.ainvoke({"messages": messages})
